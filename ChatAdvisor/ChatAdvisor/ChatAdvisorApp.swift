//
//  ChatAdvisorApp.swift
//  JunShi
//
//  Created by zwt on 2024/4/8.
//

import SwiftUI

@main
struct ChatAdvisorApp: App {
    @UIApplicationDelegateAdaptor(AppDelegate.self) var appDelegate
    @ObservedObject private var authManager = AccountManager.shared
    @ObservedObject private var versionUpdateManager = VersionUpdateManager.shared
    @State private var isInitialized = false

    var body: some Scene {
        WindowGroup {
            ZStack {
                // 主要内容
                if authManager.currentUser != nil || authManager.isLoggedIn {
                    if isInitialized {
                        ContentView()
                            .onOpenURL { url in
                                AuthViewModel.shared.isLoading = false
                                debugPrint(String(format: "universalLink = %@", url.absoluteString))
                            }
                            .onAppear {
                                AppReviewManager.shared.requestReviewIfAppropriate()
                                // 应用启动后检查版本更新
                                BootManager.shared.checkForUpdates()
                            }
                    } else {
                        SplashView(isInitialized: $isInitialized)
                            .onOpenURL { url in
                                AuthViewModel.shared.isLoading = false
                                debugPrint(String(format: "universalLink = %@", url.absoluteString))
                            }
                            .onAppear {
                                // {{ AURA-X: Modify - 确保每次进入SplashView时重置AppStartupManager. Approval: mcp-feedback-enhanced(ID:20250129008). }}
                                Task { @MainActor in
                                    AppStartupManager.shared.reset()
                                }
                            }
                    }
                } else {
                    LoginView(isLoggedIn: $authManager.isLoggedIn)
                        .onOpenURL { url in
                            // 处理Twitter登录回调
                            AuthViewModel.shared.isLoading = false
                            if url.scheme == "twitterkit-\(AuthViewModel.shared.twitterApiKey)" {
                                AuthViewModel.shared.twitterApplication(open: url)
                            }
                        }
                        .onAppear {
                            // {{ AURA-X: Modify - 进入登录页面时重置初始化状态. Approval: mcp-feedback-enhanced(ID:20250129008). }}
                            isInitialized = false
                        }
                }

                // 版本更新提示覆盖层
                if versionUpdateManager.showUpdateAlert,
                   let updateInfo = versionUpdateManager.updateInfo {
                    UpdatePromptView(
                        versionControl: updateInfo,
                        onUpdate: {
                            versionUpdateManager.userDidChooseUpdate()
                        },
                        onLater: updateInfo.updateType.isForceUpdate ? nil : {
                            versionUpdateManager.userDidChooseLater()
                        },
                        onSkip: updateInfo.updateType.isForceUpdate ? nil : {
                            versionUpdateManager.userDidSkipVersion()
                        }
                    )
                    .transition(.opacity.combined(with: .scale(scale: 0.9)))
                    .animation(.easeInOut(duration: 0.3), value: versionUpdateManager.showUpdateAlert)
                    .zIndex(1000) // 确保在最顶层显示
                }
            }
        }
    }
}
