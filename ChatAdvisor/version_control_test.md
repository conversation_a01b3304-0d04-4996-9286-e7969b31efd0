# 版本控制功能测试指南

## 修复内容总结

### 1. 数据结构修复
- ✅ 更新了 `VersionCheckResponse` 结构，添加了 `versionInfo` 字段
- ✅ 确保与后端API返回的数据结构完全一致

### 2. 逻辑简化
- ✅ 移除了 `BootManager` 中重复的版本检查逻辑
- ✅ 统一使用 `/checkVersion` 接口进行版本检查
- ✅ 清理了 `getConfig` 方法中的版本控制处理代码

### 3. 触发时机优化
- ✅ 优化了版本检查的调用时机，在应用启动1秒后执行
- ✅ 确保在主线程执行UI更新操作

### 4. 错误处理增强
- ✅ 改进了网络请求失败时的处理逻辑
- ✅ 增加了更详细的日志输出
- ✅ 添加了版本检查状态重置方法

## 测试步骤

### 前置条件
1. 确保后台admin-frontend可以正常访问
2. 确保iOS应用可以正常编译和运行
3. 确保网络连接正常

### 测试场景1：可选更新
1. 在后台配置页面设置：
   - `latestVersion`: "1.2.0"
   - `minimumVersion`: "1.0.0" 
   - `updateType`: "optional"
   - `versionCheckEnabled`: true

2. 确保iOS应用当前版本低于1.2.0
3. 启动应用，等待1-2秒
4. 预期结果：显示可选更新弹窗，包含"立即更新"、"稍后提醒"、"跳过此版本"按钮

### 测试场景2：强制更新
1. 在后台配置页面设置：
   - `latestVersion`: "2.0.0"
   - `minimumVersion`: "2.0.0"
   - `updateType`: "force"
   - `versionCheckEnabled`: true

2. 确保iOS应用当前版本低于2.0.0
3. 启动应用，等待1-2秒
4. 预期结果：显示强制更新弹窗，只有"立即更新"按钮

### 测试场景3：无需更新
1. 在后台配置页面设置：
   - `latestVersion`: 等于或低于当前应用版本
   - `minimumVersion`: 低于当前应用版本

2. 启动应用
3. 预期结果：不显示更新弹窗

### 测试场景4：版本检查禁用
1. 在后台配置页面设置：
   - `versionCheckEnabled`: false

2. 启动应用
3. 预期结果：不显示更新弹窗

## 调试方法

### 1. 查看日志
在Xcode控制台中查找以下日志：
- "开始检查版本更新... 当前版本: X.X.X"
- "版本检查成功 - 需要更新: true/false, 更新类型: force/optional/none"
- "检测到版本更新需求，准备显示更新提示"
- "显示版本更新提示：X.X.X (类型: force/optional)"

### 2. 重置测试状态
如果需要重复测试，可以在代码中调用：
```swift
VersionUpdateManager.shared.resetVersionCheckState()
```

### 3. 强制检查版本
可以在代码中调用：
```swift
VersionUpdateManager.shared.checkForUpdates(force: true)
```

## 常见问题排查

### 1. 弹窗不显示
- 检查网络连接是否正常
- 检查后台配置是否正确
- 查看控制台日志确认版本检查是否成功
- 确认应用版本号是否符合更新条件

### 2. 版本比较错误
- 确认版本号格式为 x.y.z（如 1.2.0）
- 检查后台配置的版本号是否有效
- 查看日志中的版本比较结果

### 3. 网络请求失败
- 检查API端点是否可访问
- 确认请求头中包含正确的版本信息
- 查看网络错误日志

## 验收标准

✅ 后台配置最低支持版本后，客户端能正确检测到版本更新需求
✅ 根据配置正确显示强制更新或可选更新弹窗
✅ 版本比较逻辑准确工作，支持语义化版本号
✅ 用户交互流畅，支持立即更新、稍后提醒、跳过版本等操作
✅ 应用商店跳转功能正常
✅ 24小时检查间隔控制正常工作
✅ 错误处理健壮，网络失败时不影响应用正常使用
