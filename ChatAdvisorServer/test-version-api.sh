#!/bin/bash

# 版本控制API测试脚本
# 用于测试版本检查接口的各种场景

BASE_URL="http://localhost:33001"

echo "=== 版本控制API测试 ==="
echo ""

# 测试1：可选更新场景
echo "测试1：可选更新场景（当前版本1.2.3，最新版本1.2.4）"
echo "----------------------------------------"
curl -s -X GET "$BASE_URL/checkVersion" \
  -H "app-version: 1.2.3" \
  -H "platform: ios" \
  -H "local: zh_CN" | jq '.'
echo ""

# 测试2：强制更新场景（假设最低版本设为1.2.4）
echo "测试2：强制更新场景（当前版本1.2.2，最低版本1.2.4）"
echo "----------------------------------------"
curl -s -X GET "$BASE_URL/checkVersion" \
  -H "app-version: 1.2.2" \
  -H "platform: ios" \
  -H "local: zh_CN" | jq '.'
echo ""

# 测试3：无需更新场景
echo "测试3：无需更新场景（当前版本1.2.4，最新版本1.2.4）"
echo "----------------------------------------"
curl -s -X GET "$BASE_URL/checkVersion" \
  -H "app-version: 1.2.4" \
  -H "platform: ios" \
  -H "local: zh_CN" | jq '.'
echo ""

# 测试4：Android平台
echo "测试4：Android平台测试"
echo "----------------------------------------"
curl -s -X GET "$BASE_URL/checkVersion" \
  -H "app-version: 1.2.3" \
  -H "platform: android" \
  -H "local: en" | jq '.'
echo ""

# 测试5：getConfig接口
echo "测试5：getConfig接口测试"
echo "----------------------------------------"
curl -s -X GET "$BASE_URL/getConfig" \
  -H "app-version: 1.2.3" \
  -H "platform: ios" \
  -H "local: zh_CN" | jq '.data.versionControl'
echo ""

echo "=== 测试完成 ==="
